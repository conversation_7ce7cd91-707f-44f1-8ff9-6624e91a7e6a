import { NextRequest, NextResponse } from 'next/server';
import {
  getTargetHost,
  isValidProxyRequest,
  buildTargetUrl,
  logProxyRequest,
  logProxyError,
  proxyOptions
} from './config/proxy';

export async function middleware(request: NextRequest) {
  const url = new URL(request.url);

  // 如果是 Next.js 内部路径，跳过代理
  if (url.pathname.startsWith('/_next') ||
      url.pathname.startsWith('/api') ||
      url.pathname.startsWith('/__nextjs') ||
      url.pathname.includes('.')) {
    return NextResponse.next();
  }

  // 检查是否为有效的代理请求
  if (!isValidProxyRequest(url.hostname)) {
    return NextResponse.next();
  }

  const wordsArr = url.host.split(".");
  const subdomain = wordsArr[0];

  // 获取目标主机
  const targetHost = getTargetHost(subdomain);
  if (!targetHost) {
    // 如果没有匹配的子域名，返回 404 页面
    return new NextResponse(
      `<html><body><h1>404 - 未找到子域名 "${subdomain}" 对应的目标主机</h1></body></html>`,
      {
        status: 404,
        headers: { 'content-type': 'text/html' }
      }
    );
  }

  try {
    // 记录代理请求
    logProxyRequest(subdomain, targetHost, request.method, url.pathname);

    // 构建目标 URL
    const targetUrl = buildTargetUrl(request.url, targetHost);

    // 准备请求头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 过滤掉不应该转发的头
      if (!proxyOptions.filteredRequestHeaders.includes(key.toLowerCase())) {
        headers.set(key, value);
      }
    });

    // 设置正确的 Host 头
    headers.set('host', targetHost);

    // 准备请求体
    let body: BodyInit | null = null;
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      body = await request.blob();
    }

    // 发起代理请求
    const proxyResponse = await fetch(targetUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });

    // 创建响应头
    const responseHeaders = new Headers();
    proxyResponse.headers.forEach((value, key) => {
      // 过滤掉不应该返回的头
      if (!proxyOptions.filteredResponseHeaders.includes(key.toLowerCase())) {
        responseHeaders.set(key, value);
      }
    });

    // 添加自定义头
    Object.entries(proxyOptions.customHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    // 获取响应体
    const responseBody = await proxyResponse.arrayBuffer();

    // 返回代理响应
    return new NextResponse(responseBody, {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    logProxyError(error as Error, subdomain, targetHost);
    return new NextResponse(
      `<html><body><h1>500 - 代理请求失败</h1><p>${error instanceof Error ? error.message : '未知错误'}</p></body></html>`,
      {
        status: 500,
        headers: { 'content-type': 'text/html' }
      }
    );
  }
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了：
     * - api 路由 (以 /api 开头)
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     * - 其他静态资源文件
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
}
