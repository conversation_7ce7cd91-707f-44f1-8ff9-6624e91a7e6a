{"name": "reverse-proxy", "version": "1.0.0", "description": "基于 EdgeOne Pages Functions 的反向代理服务", "build": {"command": "echo 'Static files ready'", "outputDirectory": "static"}, "functions": {"directory": "functions"}, "routes": [{"src": "/(.*)", "dest": "/functions/proxy.js"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Powered-By", "value": "Edge<PERSON>ne Pages"}, {"key": "X-Proxy-Service", "value": "EdgeOne Reverse Proxy"}]}], "redirects": [], "rewrites": [{"source": "/(.*)", "destination": "/functions/proxy.js"}]}