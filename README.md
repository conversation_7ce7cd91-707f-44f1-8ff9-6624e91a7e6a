# Next.js 反向代理服务

基于 Cloudflare Worker 的 cfwork.js 实现的 Next.js 反向代理服务。

## 功能特性

- ✅ 支持所有 HTTP 方法 (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
- ✅ 自动转发请求头和请求体
- ✅ 保持原始响应状态码和内容
- ✅ 添加自定义响应头
- ✅ 错误处理和日志记录
- ✅ 基于 Next.js 中间件实现
- ✅ 支持子域名到目标主机的映射

## 域名映射配置

当前配置的域名映射（在 `src/config/proxy.ts` 中）：

| 子域名 | 目标主机 |
|--------|----------|
| jg.localhost:3000 | jpdy.dataoke.vip |
| cms.localhost:3000 | bpcgg.yhzu.cn |
| hdk.localhost:3000 | hdk.yawols.cn |
| hdkcms.localhost:3000 | cms.youhuiquan.cloudns.org |

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

服务器将在 [http://localhost:3000](http://localhost:3000) 启动。

### 3. 配置域名解析

为了测试反向代理功能，您需要将子域名解析到 localhost:3000。

#### 方法一：修改 hosts 文件

在您的 hosts 文件中添加以下条目：

**Windows**: `C:\Windows\System32\drivers\etc\hosts`
**macOS/Linux**: `/etc/hosts`

```
127.0.0.1 jg.localhost
127.0.0.1 cms.localhost
127.0.0.1 hdk.localhost
127.0.0.1 hdkcms.localhost
```

#### 方法二：使用本地 DNS 服务

您也可以使用 dnsmasq 或其他本地 DNS 服务来配置通配符域名解析。

### 4. 测试代理功能

配置完成后，访问以下 URL 来测试代理功能：

- http://jg.localhost:3000 → 代理到 jpdy.dataoke.vip
- http://cms.localhost:3000 → 代理到 bpcgg.yhzu.cn
- http://hdk.localhost:3000 → 代理到 hdk.yawols.cn
- http://hdkcms.localhost:3000 → 代理到 cms.youhuiquan.cloudns.org

## 项目结构

```
src/
├── app/
│   ├── api/
│   │   └── [...proxy]/
│   │       └── route.ts          # API 路由代理处理器
│   ├── page.tsx                  # 主页面（显示配置信息）
│   └── layout.tsx               # 布局组件
├── config/
│   └── proxy.ts                 # 代理配置文件
└── middleware.ts                # Next.js 中间件（主要代理逻辑）
```

## 配置说明

### 修改域名映射

编辑 `src/config/proxy.ts` 文件中的 `toHost` 对象：

```typescript
export const toHost: ProxyConfig = {
  "subdomain": "target-host.com",
  // 添加更多映射...
};
```

### 自定义配置

在 `src/config/proxy.ts` 中，您可以修改以下配置：

- `protocol`: 目标服务器协议（默认 "http:"）
- `customHeaders`: 自定义响应头
- `filteredRequestHeaders`: 过滤的请求头
- `filteredResponseHeaders`: 过滤的响应头
- `timeout`: 请求超时时间
- `enableLogging`: 是否启用日志

## 部署

### 开发环境

```bash
npm run dev
```

### 生产环境

```bash
npm run build
npm start
```

### 部署到 Vercel

```bash
npm install -g vercel
vercel
```

## 与 Cloudflare Worker 的对比

| 特性 | Cloudflare Worker | Next.js 版本 |
|------|-------------------|--------------|
| 部署复杂度 | 简单 | 中等 |
| 自定义能力 | 有限 | 强大 |
| 本地开发 | 需要工具 | 原生支持 |
| 扩展性 | 有限 | 高 |
| 成本 | 按请求计费 | 服务器成本 |

## 故障排除

### 1. 域名解析问题

确保您的 hosts 文件配置正确，或者使用 `nslookup` 命令验证域名解析：

```bash
nslookup jg.localhost
```

### 2. 端口冲突

如果 3000 端口被占用，可以使用其他端口：

```bash
npm run dev -- -p 3001
```

### 3. 代理请求失败

检查控制台日志，确认目标服务器是否可访问。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
