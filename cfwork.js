addEventListener("fetch", (event) => {
  event.respondWith(handleRequest(event.request));
});

let toHost = {
  "jg": "jpdy.dataoke.vip",
  "cms": "bpcgg.yhzu.cn",
  "hdk": "hdk.yawols.cn",
  "hdkcms": "cms.youhuiquan.cloudns.org"
};

async function handleRequest(request) {
  const url = new URL(request.url);
  let wordsArr = url.host.split(".");
  
  // 修改目标主机
  url.host = toHost[wordsArr[0]];
  url.protocol = "http:";  // 强制使用 HTTP 协议

  // 获取原始响应
  const originResponse = await fetch(url, {
    headers: request.headers,
    method: request.method,
    body: request.body
  });

  // 克隆响应头并添加自定义头
  const newHeaders = new Headers(originResponse.headers);
  newHeaders.set("posered", "bbs.weiququ.cn");
  // newHeaders.append("Set-Cookie", "key=value");     // 追加多个值用 append

  // 构建新响应（保留原始状态和内容）
  return new Response(originResponse.body, {
    status: originResponse.status,
    statusText: originResponse.statusText,
    headers: newHeaders
  });
}