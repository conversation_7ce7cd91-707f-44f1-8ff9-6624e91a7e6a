// 反向代理配置文件
// 对应 cfwork.js 中的 toHost 配置

export interface ProxyConfig {
  [subdomain: string]: string;
}

// 域名映射配置
export const toHost: ProxyConfig = {
  "jg": "jpdy.dataoke.vip",
  "cms": "bpcgg.yhzu.cn", 
  "hdk": "hdk.yawols.cn",
  "hdkcms": "cms.youhuiquan.cloudns.org"
};

// 代理配置选项
export const proxyOptions = {
  // 强制使用的协议
  protocol: "http:" as const,
  
  // 自定义响应头
  customHeaders: {
    "posered": "bbs.weiququ.cn"
  },
  
  // 需要过滤的请求头（不转发到目标服务器）
  filteredRequestHeaders: [
    'host', 
    'connection', 
    'upgrade'
  ],
  
  // 需要过滤的响应头（不返回给客户端）
  filteredResponseHeaders: [
    'connection', 
    'transfer-encoding', 
    'content-encoding'
  ],
  
  // 超时设置（毫秒）
  timeout: 30000,
  
  // 是否启用日志
  enableLogging: true
};

// 获取目标主机
export function getTargetHost(subdomain: string): string | null {
  return toHost[subdomain] || null;
}

// 检查是否为有效的代理请求
export function isValidProxyRequest(hostname: string): boolean {
  // 跳过 localhost 和 IP 地址
  if (hostname === 'localhost' || hostname === '127.0.0.1' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return false;
  }
  
  const parts = hostname.split('.');
  if (parts.length < 2) {
    return false;
  }
  
  const subdomain = parts[0];
  return subdomain in toHost;
}

// 构建目标 URL
export function buildTargetUrl(originalUrl: string, targetHost: string): string {
  const url = new URL(originalUrl);
  url.host = targetHost;
  url.protocol = proxyOptions.protocol;
  return url.toString();
}

// 日志记录函数
export function logProxyRequest(subdomain: string, targetHost: string, method: string, path: string) {
  if (proxyOptions.enableLogging) {
    console.log(`[代理] ${method} ${subdomain} -> ${targetHost}${path}`);
  }
}

export function logProxyError(error: Error, subdomain: string, targetHost: string) {
  if (proxyOptions.enableLogging) {
    console.error(`[代理错误] ${subdomain} -> ${targetHost}:`, error.message);
  }
}
