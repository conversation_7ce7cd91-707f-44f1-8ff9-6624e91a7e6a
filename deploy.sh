#!/bin/bash

# EdgeOne Pages 反向代理部署脚本

echo "🚀 部署反向代理服务到 EdgeOne Pages..."

# 使用环境变量或默认值
PROJECT_NAME=${EDGEONE_PROJECT_NAME:-"reverse-proxy"}
TOKEN=${EDGEONE_API_TOKEN}

if [ -z "$TOKEN" ]; then
    echo "❌ 请设置环境变量 EDGEONE_API_TOKEN"
    echo "使用方法: EDGEONE_API_TOKEN=your_token ./deploy.sh"
    exit 1
fi

echo "📦 部署项目: $PROJECT_NAME"
edgeone pages deploy . -n "$PROJECT_NAME" -t "$TOKEN"

if [ $? -eq 0 ]; then
    echo "✅ 部署成功！"
    echo "🌍 反向代理服务已部署到 EdgeOne Pages"
    echo "🔧 修改配置请编辑 functions/index.js 文件"
else
    echo "❌ 部署失败"
    exit 1
fi
