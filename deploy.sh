#!/bin/bash

# EdgeOne Pages 部署脚本

echo "🚀 开始部署 EdgeOne Pages 反向代理服务..."

# 检查是否安装了 EdgeOne CLI
if ! command -v edgeone &> /dev/null; then
    echo "❌ EdgeOne CLI 未安装，正在安装..."
    npm install -g edgeone
fi

# 检查是否已登录
echo "🔐 检查登录状态..."
if ! edgeone whoami &> /dev/null; then
    echo "❌ 未登录，请先登录..."
    edgeone login
fi

# 创建部署目录
echo "📁 准备部署文件..."
mkdir -p deploy
cp -r static/* deploy/
cp -r functions deploy/
cp edgeone.json deploy/

# 部署到 EdgeOne Pages
echo "🌐 部署到 EdgeOne Pages..."
cd deploy

# 使用环境变量或提示输入项目名称和 token
PROJECT_NAME=${EDGEONE_PROJECT_NAME:-"reverse-proxy-$(date +%s)"}
TOKEN=${EDGEONE_API_TOKEN}

if [ -z "$TOKEN" ]; then
    echo "❌ 请设置环境变量 EDGEONE_API_TOKEN 或在命令行中提供 token"
    echo "使用方法: EDGEONE_API_TOKEN=your_token ./deploy.sh"
    exit 1
fi

echo "📦 部署项目: $PROJECT_NAME"
edgeone pages deploy . -n "$PROJECT_NAME" -t "$TOKEN"

if [ $? -eq 0 ]; then
    echo "✅ 部署成功！"
    echo "🌍 您的反向代理服务已部署到 EdgeOne Pages"
    echo "📝 请在 EdgeOne Pages 控制台配置自定义域名"
    echo "🔧 如需修改代理配置，请编辑 functions/proxy.js 文件"
else
    echo "❌ 部署失败，请检查错误信息"
    exit 1
fi

# 清理临时文件
cd ..
rm -rf deploy

echo "🎉 部署完成！"
