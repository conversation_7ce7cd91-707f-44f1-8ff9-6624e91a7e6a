<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeOne Pages 反向代理服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #0070f3;
            margin-bottom: 1rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            color: #555;
            margin-bottom: 1rem;
            border-bottom: 2px solid #0070f3;
            padding-bottom: 0.5rem;
        }
        
        .mapping-grid {
            display: grid;
            gap: 1rem;
        }
        
        .mapping-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #0070f3;
        }
        
        .mapping-item strong {
            color: #0070f3;
        }
        
        .arrow {
            color: #666;
            font-size: 1.2rem;
        }
        
        .target {
            color: #28a745;
            font-weight: 500;
        }
        
        .instructions {
            background: #e8f4fd;
            border-left: 4px solid #0070f3;
        }
        
        .features {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .features h2 {
            border-bottom-color: #ffc107;
        }
        
        .list {
            list-style: none;
            padding-left: 0;
        }
        
        .list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .list li:last-child {
            border-bottom: none;
        }
        
        .list li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }
        
        .code {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9rem;
        }
        
        .steps {
            counter-reset: step-counter;
        }
        
        .steps li {
            counter-increment: step-counter;
            position: relative;
            padding-left: 2rem;
        }
        
        .steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #0070f3;
            color: white;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .sub-list {
            margin-top: 0.5rem;
            margin-left: 1rem;
        }
        
        .sub-list li::before {
            content: "• ";
            color: #0070f3;
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EdgeOne Pages 反向代理服务</h1>
            <p>基于 Cloudflare Worker 的 cfwork.js 实现的反向代理服务</p>
        </div>

        <div class="card">
            <h2>配置的域名映射</h2>
            <div class="mapping-grid">
                <div class="mapping-item">
                    <strong>jg.your-domain.com</strong>
                    <span class="arrow">→</span>
                    <span class="target">http://jpdy.dataoke.vip</span>
                </div>
                <div class="mapping-item">
                    <strong>cms.your-domain.com</strong>
                    <span class="arrow">→</span>
                    <span class="target">http://bpcgg.yhzu.cn</span>
                </div>
                <div class="mapping-item">
                    <strong>hdk.your-domain.com</strong>
                    <span class="arrow">→</span>
                    <span class="target">http://hdk.yawols.cn</span>
                </div>
                <div class="mapping-item">
                    <strong>hdkcms.your-domain.com</strong>
                    <span class="arrow">→</span>
                    <span class="target">http://cms.youhuiquan.cloudns.org</span>
                </div>
            </div>
        </div>

        <div class="card instructions">
            <h2>部署说明</h2>
            <ol class="list steps">
                <li>
                    <strong>使用 EdgeOne CLI 部署：</strong>
                    <div class="sub-list">
                        <div>安装 CLI: <span class="code">npm install -g edgeone</span></div>
                        <div>登录: <span class="code">edgeone login</span></div>
                        <div>初始化: <span class="code">edgeone pages init</span></div>
                        <div>部署静态文件: <span class="code">edgeone pages deploy ./static -n your-project-name -t YOUR_TOKEN</span></div>
                    </div>
                </li>
                <li>
                    <strong>配置 Pages Functions：</strong>
                    <div class="sub-list">
                        <div>将 <span class="code">functions/proxy.js</span> 上传到项目</div>
                        <div>配置路由规则指向 Functions</div>
                    </div>
                </li>
                <li>
                    <strong>配置自定义域名：</strong>
                    <div class="sub-list">
                        <div>在 EdgeOne Pages 控制台添加自定义域名</div>
                        <div>配置 DNS CNAME 记录</div>
                        <div>设置 SSL 证书</div>
                    </div>
                </li>
                <li>
                    <strong>测试代理功能：</strong>
                    <div class="sub-list">
                        <div>访问配置的子域名</div>
                        <div>验证请求是否正确转发到目标服务器</div>
                    </div>
                </li>
            </ol>
        </div>

        <div class="card features">
            <h2>技术特性</h2>
            <ul class="list">
                <li>支持所有 HTTP 方法 (GET, POST, PUT, DELETE, etc.)</li>
                <li>自动转发请求头和请求体</li>
                <li>保持原始响应状态码和内容</li>
                <li>添加自定义响应头</li>
                <li>错误处理和日志记录</li>
                <li>基于 EdgeOne Pages Functions 实现</li>
                <li>全球边缘节点部署</li>
                <li>高性能和低延迟</li>
            </ul>
        </div>

        <div class="footer">
            <p>EdgeOne Pages 反向代理服务 | 基于腾讯云 EdgeOne</p>
            <p>如需修改配置，请编辑 <span class="code">functions/proxy.js</span> 文件</p>
        </div>
    </div>
</body>
</html>
