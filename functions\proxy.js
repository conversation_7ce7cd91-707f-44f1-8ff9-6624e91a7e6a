// EdgeOne Pages Function 反向代理
// 基于 cfwork.js 的逻辑

// 域名映射配置，对应 cfwork.js 中的 toHost
const toHost = {
  "jg": "jpdy.dataoke.vip",
  "cms": "bpcgg.yhzu.cn", 
  "hdk": "hdk.yawols.cn",
  "hdkcms": "cms.youhuiquan.cloudns.org"
};

export default async function handler(request) {
  try {
    const url = new URL(request.url);
    const wordsArr = url.host.split(".");
    
    // 获取子域名前缀
    const subdomain = wordsArr[0];
    
    // 检查是否有对应的目标主机
    const targetHost = toHost[subdomain];
    if (!targetHost) {
      return new Response(
        JSON.stringify({ error: `未找到子域名 "${subdomain}" 对应的目标主机` }),
        { 
          status: 404,
          headers: { 'content-type': 'application/json' }
        }
      );
    }

    // 构建目标 URL
    const targetUrl = new URL(url);
    targetUrl.host = targetHost;
    targetUrl.protocol = "http:"; // 强制使用 HTTP 协议，对应 cfwork.js

    // 准备请求头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 过滤掉一些不应该转发的头
      if (!['host', 'connection', 'upgrade'].includes(key.toLowerCase())) {
        headers.set(key, value);
      }
    });

    // 设置正确的 Host 头
    headers.set('host', targetHost);

    // 准备请求体
    let body = null;
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      body = await request.arrayBuffer();
    }

    // 发起代理请求
    const proxyResponse = await fetch(targetUrl.toString(), {
      method: request.method,
      headers: headers,
      body: body,
    });

    // 创建响应头
    const responseHeaders = new Headers();
    proxyResponse.headers.forEach((value, key) => {
      // 过滤掉一些不应该返回的头
      if (!['connection', 'transfer-encoding', 'content-encoding'].includes(key.toLowerCase())) {
        responseHeaders.set(key, value);
      }
    });

    // 添加自定义头，对应 cfwork.js 中的逻辑
    responseHeaders.set("posered", "bbs.weiququ.cn");

    // 获取响应体
    const responseBody = await proxyResponse.arrayBuffer();

    // 返回代理响应
    return new Response(responseBody, {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error('代理请求失败:', error);
    return new Response(
      JSON.stringify({ 
        error: '代理请求失败', 
        details: error.message 
      }),
      { 
        status: 500,
        headers: { 'content-type': 'application/json' }
      }
    );
  }
}
