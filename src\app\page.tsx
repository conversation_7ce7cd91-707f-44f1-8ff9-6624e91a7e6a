import styles from "./page.module.css";
import { toHost } from "../config/proxy";

export default function Home() {
  return (
    <div className={styles.page}>
      <main className={styles.main}>
        <h1 style={{ fontSize: '2rem', marginBottom: '2rem', color: '#333' }}>
          Next.js 反向代理服务
        </h1>

        <div style={{
          background: '#f5f5f5',
          padding: '2rem',
          borderRadius: '8px',
          marginBottom: '2rem',
          maxWidth: '800px'
        }}>
          <h2 style={{ marginBottom: '1rem', color: '#555' }}>配置的域名映射</h2>
          <div style={{ display: 'grid', gap: '1rem' }}>
            {Object.entries(toHost).map(([subdomain, target]) => (
              <div key={subdomain} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '1rem',
                background: 'white',
                borderRadius: '4px',
                border: '1px solid #ddd'
              }}>
                <div>
                  <strong>{subdomain}.localhost:3000</strong>
                </div>
                <div style={{ color: '#666' }}>→</div>
                <div style={{ color: '#0070f3' }}>
                  http://{target}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div style={{
          background: '#e8f4fd',
          padding: '2rem',
          borderRadius: '8px',
          marginBottom: '2rem',
          maxWidth: '800px'
        }}>
          <h2 style={{ marginBottom: '1rem', color: '#0070f3' }}>使用说明</h2>
          <ol style={{ lineHeight: '1.8', color: '#333' }}>
            <li>
              <strong>启动服务：</strong> 运行 <code style={{ background: '#f0f0f0', padding: '2px 6px', borderRadius: '3px' }}>npm run dev</code>
            </li>
            <li>
              <strong>配置域名解析：</strong> 将以下域名解析到 localhost:3000
              <ul style={{ marginTop: '0.5rem', marginLeft: '1rem' }}>
                {Object.keys(toHost).map(subdomain => (
                  <li key={subdomain}>
                    <code style={{ background: '#f0f0f0', padding: '2px 6px', borderRadius: '3px' }}>
                      {subdomain}.localhost
                    </code>
                  </li>
                ))}
              </ul>
            </li>
            <li>
              <strong>测试代理：</strong> 访问配置的子域名，请求将被转发到对应的目标服务器
            </li>
          </ol>
        </div>

        <div style={{
          background: '#fff3cd',
          padding: '2rem',
          borderRadius: '8px',
          maxWidth: '800px'
        }}>
          <h2 style={{ marginBottom: '1rem', color: '#856404' }}>技术特性</h2>
          <ul style={{ lineHeight: '1.8', color: '#333' }}>
            <li>✅ 支持所有 HTTP 方法 (GET, POST, PUT, DELETE, etc.)</li>
            <li>✅ 自动转发请求头和请求体</li>
            <li>✅ 保持原始响应状态码和内容</li>
            <li>✅ 添加自定义响应头</li>
            <li>✅ 错误处理和日志记录</li>
            <li>✅ 基于 Next.js 中间件实现</li>
          </ul>
        </div>
      </main>
    </div>
  );
}
