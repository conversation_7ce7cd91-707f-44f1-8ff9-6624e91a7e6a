# EdgeOne Pages 部署脚本 (PowerShell)

Write-Host "🚀 开始部署 EdgeOne Pages 反向代理服务..." -ForegroundColor Green

# 检查是否安装了 EdgeOne CLI
try {
    edgeone -v | Out-Null
    Write-Host "✅ EdgeOne CLI 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ EdgeOne CLI 未安装，正在安装..." -ForegroundColor Yellow
    npm install -g edgeone
}

# 检查是否已登录
Write-Host "🔐 检查登录状态..." -ForegroundColor Blue
try {
    edgeone whoami | Out-Null
    Write-Host "✅ 已登录 EdgeOne" -ForegroundColor Green
} catch {
    Write-Host "❌ 未登录，请先登录..." -ForegroundColor Red
    edgeone login
}

# 创建部署目录
Write-Host "📁 准备部署文件..." -ForegroundColor Blue
if (Test-Path "deploy") {
    Remove-Item -Recurse -Force "deploy"
}
New-Item -ItemType Directory -Path "deploy" | Out-Null

# 复制文件
Copy-Item -Recurse "static\*" "deploy\"
Copy-Item -Recurse "functions" "deploy\"
Copy-Item "edgeone.json" "deploy\"

# 部署到 EdgeOne Pages
Write-Host "🌐 部署到 EdgeOne Pages..." -ForegroundColor Blue
Set-Location "deploy"

# 使用环境变量或提示输入项目名称和 token
$PROJECT_NAME = $env:EDGEONE_PROJECT_NAME
if (-not $PROJECT_NAME) {
    $timestamp = [int][double]::Parse((Get-Date -UFormat %s))
    $PROJECT_NAME = "reverse-proxy-$timestamp"
}

$TOKEN = $env:EDGEONE_API_TOKEN
if (-not $TOKEN) {
    Write-Host "❌ 请设置环境变量 EDGEONE_API_TOKEN 或在命令行中提供 token" -ForegroundColor Red
    Write-Host "使用方法: `$env:EDGEONE_API_TOKEN='your_token'; .\deploy.ps1" -ForegroundColor Yellow
    Set-Location ".."
    exit 1
}

Write-Host "📦 部署项目: $PROJECT_NAME" -ForegroundColor Blue
try {
    edgeone pages deploy . -n $PROJECT_NAME -t $TOKEN
    
    Write-Host "✅ 部署成功！" -ForegroundColor Green
    Write-Host "🌍 您的反向代理服务已部署到 EdgeOne Pages" -ForegroundColor Green
    Write-Host "📝 请在 EdgeOne Pages 控制台配置自定义域名" -ForegroundColor Yellow
    Write-Host "🔧 如需修改代理配置，请编辑 functions/proxy.js 文件" -ForegroundColor Yellow
} catch {
    Write-Host "❌ 部署失败，请检查错误信息" -ForegroundColor Red
    Set-Location ".."
    exit 1
}

# 清理临时文件
Set-Location ".."
Remove-Item -Recurse -Force "deploy"

Write-Host "🎉 部署完成！" -ForegroundColor Green
