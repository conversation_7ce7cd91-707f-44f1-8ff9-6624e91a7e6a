import { NextRequest, NextResponse } from 'next/server';
import {
  getTargetHost,
  buildTargetUrl,
  logProxyRequest,
  logProxyError,
  proxyOptions
} from '../../../config/proxy';

export async function GET(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function POST(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function PUT(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function DELETE(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function PATCH(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function HEAD(request: NextRequest) {
  return handleProxyRequest(request);
}

export async function OPTIONS(request: NextRequest) {
  return handleProxyRequest(request);
}

async function handleProxyRequest(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const wordsArr = url.host.split(".");

    // 获取子域名前缀
    const subdomain = wordsArr[0];

    // 获取目标主机
    const targetHost = getTargetHost(subdomain);
    if (!targetHost) {
      return NextResponse.json(
        { error: `未找到子域名 "${subdomain}" 对应的目标主机` },
        { status: 404 }
      );
    }

    // 记录代理请求
    logProxyRequest(subdomain, targetHost, request.method, url.pathname);

    // 构建目标 URL
    const targetUrl = buildTargetUrl(request.url, targetHost);

    // 准备请求头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 过滤掉不应该转发的头
      if (!proxyOptions.filteredRequestHeaders.includes(key.toLowerCase())) {
        headers.set(key, value);
      }
    });

    // 设置正确的 Host 头
    headers.set('host', targetHost);

    // 准备请求体
    let body: BodyInit | null = null;
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      body = await request.blob();
    }

    // 发起代理请求
    const proxyResponse = await fetch(targetUrl, {
      method: request.method,
      headers: headers,
      body: body,
    });

    // 创建响应头
    const responseHeaders = new Headers();
    proxyResponse.headers.forEach((value, key) => {
      // 过滤掉不应该返回的头
      if (!proxyOptions.filteredResponseHeaders.includes(key.toLowerCase())) {
        responseHeaders.set(key, value);
      }
    });

    // 添加自定义头
    Object.entries(proxyOptions.customHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value);
    });

    // 获取响应体
    const responseBody = await proxyResponse.arrayBuffer();

    // 返回代理响应
    return new NextResponse(responseBody, {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    const subdomain = new URL(request.url).host.split(".")[0];
    const targetHost = getTargetHost(subdomain);
    logProxyError(error as Error, subdomain, targetHost || 'unknown');
    return NextResponse.json(
      { error: '代理请求失败', details: error instanceof Error ? error.message : '未知错误' },
      { status: 500 }
    );
  }
}
