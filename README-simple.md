# EdgeOne Pages 反向代理

基于 cfwork.js 的简化版反向代理服务，使用 EdgeOne Pages Functions 实现。

## 项目结构

```
├── functions/
│   └── index.js        # 反向代理函数（处理所有请求）
├── edgeone.json        # EdgeOne Pages 配置
├── deploy.sh           # 部署脚本
└── README-simple.md    # 说明文档
```

## 域名映射配置

当前配置的域名映射：

| 子域名 | 目标主机 |
|--------|----------|
| jg.your-domain.com | jpdy.dataoke.vip |
| cms.your-domain.com | bpcgg.yhzu.cn |
| hdk.your-domain.com | hdk.yawols.cn |
| hdkcms.your-domain.com | cms.youhuiquan.cloudns.org |

## 快速部署

### 1. 安装 EdgeOne CLI

```bash
npm install -g edgeone
```

### 2. 登录

```bash
edgeone login
```

### 3. 部署

```bash
# 设置 API Token
export EDGEONE_API_TOKEN="your_api_token"

# 部署
chmod +x deploy.sh
./deploy.sh
```

或者直接使用命令：

```bash
edgeone pages deploy . -n reverse-proxy -t your_api_token
```

## 修改配置

编辑 `functions/index.js` 文件中的 `toHost` 对象：

```javascript
const toHost = {
  "subdomain": "target-host.com",
  // 添加更多映射...
};
```

## 功能特性

- ✅ 支持所有 HTTP 方法
- ✅ 自动转发请求头和请求体
- ✅ 保持原始响应状态码
- ✅ 添加自定义响应头
- ✅ 内置配置页面
- ✅ 错误处理
- ✅ 全球边缘节点部署

## 使用说明

1. 部署成功后，在 EdgeOne Pages 控制台配置自定义域名
2. 设置 DNS CNAME 记录指向 EdgeOne Pages 提供的域名
3. 访问配置的子域名，请求将自动转发到目标服务器
4. 访问根域名可查看配置信息和服务状态

## 故障排除

### 部署失败
- 检查 API Token 是否正确
- 确认已登录 EdgeOne CLI
- 验证网络连接

### 代理不工作
- 检查自定义域名配置
- 验证 DNS 解析
- 确认目标服务器可访问

### 修改配置
- 编辑 `functions/index.js`
- 重新部署: `./deploy.sh`

## 许可证

MIT License
