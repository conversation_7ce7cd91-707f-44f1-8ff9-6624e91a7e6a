// EdgeOne Pages Function 反向代理
// 基于 cfwork.js 的逻辑，处理所有路由

// 域名映射配置，对应 cfwork.js 中的 toHost
const toHost = {
  "jg": "jpdy.dataoke.vip",
  "cms": "bpcgg.yhzu.cn", 
  "hdk": "hdk.yawols.cn",
  "hdkcms": "cms.youhuiquan.cloudns.org"
};

export default async function handler(request) {
  try {
    const url = new URL(request.url);
    const wordsArr = url.host.split(".");
    
    // 获取子域名前缀
    const subdomain = wordsArr[0];
    
    // 如果访问根域名，显示配置信息
    if (!toHost[subdomain]) {
      return new Response(getConfigPage(), {
        status: 200,
        headers: { 'content-type': 'text/html; charset=utf-8' }
      });
    }

    const targetHost = toHost[subdomain];

    // 构建目标 URL
    const targetUrl = new URL(url);
    targetUrl.host = targetHost;
    targetUrl.protocol = "http:"; // 强制使用 HTTP 协议，对应 cfwork.js

    // 准备请求头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 过滤掉一些不应该转发的头
      if (!['host', 'connection', 'upgrade'].includes(key.toLowerCase())) {
        headers.set(key, value);
      }
    });

    // 设置正确的 Host 头
    headers.set('host', targetHost);

    // 准备请求体
    let body = null;
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      body = await request.arrayBuffer();
    }

    // 发起代理请求
    const proxyResponse = await fetch(targetUrl.toString(), {
      method: request.method,
      headers: headers,
      body: body,
    });

    // 创建响应头
    const responseHeaders = new Headers();
    proxyResponse.headers.forEach((value, key) => {
      // 过滤掉一些不应该返回的头
      if (!['connection', 'transfer-encoding', 'content-encoding'].includes(key.toLowerCase())) {
        responseHeaders.set(key, value);
      }
    });

    // 添加自定义头，对应 cfwork.js 中的逻辑
    responseHeaders.set("posered", "bbs.weiququ.cn");

    // 获取响应体
    const responseBody = await proxyResponse.arrayBuffer();

    // 返回代理响应
    return new Response(responseBody, {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error('代理请求失败:', error);
    return new Response(
      JSON.stringify({ 
        error: '代理请求失败', 
        details: error.message 
      }),
      { 
        status: 500,
        headers: { 'content-type': 'application/json' }
      }
    );
  }
}

// 生成配置页面
function getConfigPage() {
  const mappings = Object.entries(toHost)
    .map(([subdomain, target]) => 
      `<div class="mapping-item">
        <strong>${subdomain}.your-domain.com</strong>
        <span class="arrow">→</span>
        <span class="target">http://${target}</span>
      </div>`
    ).join('');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeOne Pages 反向代理服务</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; margin-bottom: 3rem; }
        .header h1 { font-size: 2.5rem; color: #0070f3; margin-bottom: 1rem; }
        .card { background: white; border-radius: 8px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h2 { color: #555; margin-bottom: 1rem; border-bottom: 2px solid #0070f3; padding-bottom: 0.5rem; }
        .mapping-item { display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #0070f3; margin-bottom: 1rem; }
        .mapping-item strong { color: #0070f3; }
        .arrow { color: #666; font-size: 1.2rem; }
        .target { color: #28a745; font-weight: 500; }
        .status { background: #d4edda; border-left-color: #28a745; }
        .footer { text-align: center; margin-top: 2rem; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 反向代理服务</h1>
            <p>EdgeOne Pages Functions 驱动</p>
        </div>
        
        <div class="card">
            <h2>📋 域名映射配置</h2>
            ${mappings}
        </div>
        
        <div class="card status">
            <h2>✅ 服务状态</h2>
            <p>反向代理服务运行正常</p>
            <p>当前时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="footer">
            <p>基于 cfwork.js 逻辑 | EdgeOne Pages Functions</p>
        </div>
    </div>
</body>
</html>`;
}
