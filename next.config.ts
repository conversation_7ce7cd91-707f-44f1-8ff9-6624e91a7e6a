import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 启用实验性功能以支持更好的代理
  experimental: {
    // 允许处理更大的请求体
    serverComponentsExternalPackages: [],
  },

  // 配置重写规则，确保所有请求都能被正确处理
  async rewrites() {
    return {
      beforeFiles: [],
      afterFiles: [],
      fallback: []
    };
  },

  // 配置头部信息
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Proxy-By',
            value: 'Next.js Reverse Proxy',
          },
        ],
      },
    ];
  },

  // 允许来自任何域名的图片
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '**',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

export default nextConfig;
