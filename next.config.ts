import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 外部包配置
  serverExternalPackages: [],

  // 配置重写规则，确保所有请求都能被正确处理
  async rewrites() {
    return {
      beforeFiles: [],
      afterFiles: [],
      fallback: []
    };
  },

  // 配置头部信息
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Proxy-By',
            value: 'Next.js Reverse Proxy',
          },
        ],
      },
    ];
  },

  // 允许来自任何域名的图片
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '**',
      },
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

export default nextConfig;
